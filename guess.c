#include <stdio.h>
#include<stdlib.h>
int main() {
    int min=1;
    int max=100;
    int random_number= (rand() % (max - min + 1)) + min;
    int guess;
    printf("Guess the number between 1 and 100\n");
    while(1) {
        printf("Enter your guess: ");
        scanf("%d", &guess);
        if(guess==random_number) {
            printf("You guessed it");
            break;
        }
        else if(guess<random_number) {
            printf("Too low, try again.\n");
        }
        else {
            printf("Too high, try again.\n");
        }
    }
    return 0;
}